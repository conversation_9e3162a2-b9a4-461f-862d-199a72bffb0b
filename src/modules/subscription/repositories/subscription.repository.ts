import { Injectable } from '@nestjs/common';
import { SubscriptionEntity, SubscriptionStatusEnum } from '../entities/subscription.entity';
import { SubscriptionModel } from 'src/database/subscription/subscription.model';

@Injectable()
export class SubscriptionRepository {
  
  async create(subscription: SubscriptionEntity): Promise<SubscriptionEntity> {
    const createdSubscription = new SubscriptionModel(subscription);
    await createdSubscription.save();
    return createdSubscription.toObject();
  }

  async findById(id: string): Promise<SubscriptionEntity | null> {
    const subscription = await SubscriptionModel.findOne({ id }).lean();
    return subscription;
  }

  async findActiveSubscription(userId: string): Promise<SubscriptionEntity | null> {
    const now = new Date();
    const subscription = await SubscriptionModel.findOne({
      userId,
      status: SubscriptionStatusEnum.Active,
      endDate: { $gt: now },
    }).lean();
    return subscription;
  }

  async findUserSubscriptions(userId: string): Promise<SubscriptionEntity[]> {
    const subscriptions = await SubscriptionModel.find({ userId }).lean();
    return subscriptions;
  }

  async updateStatus(subscriptionId: string, status: SubscriptionStatusEnum): Promise<void> {
    await SubscriptionModel.updateOne({ id: subscriptionId }, { status });
  }

  async updatePaymentId(subscriptionId: string, paymentId: string): Promise<void> {
    await SubscriptionModel.updateOne({ id: subscriptionId }, { paymentId });
  }
}
