import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles } from 'src/authentication/guards/auth-role.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { CheckoutResponseDto, FeatureAccessResponseDto, PlansResponseDto, SubscriptionCheckoutRequestDto, SubscriptionsResponseDto } from '../dtos/subscription.dto';
import { FeatureTypeEnum } from '../plans/common/entities/plans.entity';
import { SubscriptionService } from '../services/subscription.service';

@Controller('subscription')
@ApiTags('Subscription Management')
@UseGuards(OnlyAuthGuard, OnlyRoleGuard)
@ApiBearerAuth()
@Roles(Role.User)
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @Get('access/:featureType')
  @ApiOperation({ summary: 'Check access status for a feature' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Feature access status retrieved successfully',
    type: FeatureAccessResponseDto,
  })
  async getFeatureAccess(
    @Param('featureType') featureType: FeatureTypeEnum,
    @UserInfo() user: User,
  ): Promise<FeatureAccessResponseDto> {
    const result = await this.subscriptionService.checkFeatureAccess(user.id, featureType);
    return {
      data: result,
    };
  }

  @Get('plans')
  @ApiOperation({ summary: 'Get all available subscription plans' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Subscription plans retrieved successfully',
    type: PlansResponseDto,
  })
  async getPlans(): Promise<PlansResponseDto> {
    return await this.subscriptionService.getPlans();
  }

  @Post('checkout')
  @ApiOperation({ summary: 'Create subscription checkout with bKash payment' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Checkout created successfully',
    type: CheckoutResponseDto,
  })
  async createCheckout(
    @Body() checkoutDto: SubscriptionCheckoutRequestDto,
    @UserInfo() user: User,
  ): Promise<CheckoutResponseDto> {
    return await this.subscriptionService.createSubscriptionCheckout(user.id, checkoutDto);
  }

  @Get('my-subscriptions')
  @ApiOperation({ summary: 'Get user subscriptions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User subscriptions retrieved successfully',
    type: SubscriptionsResponseDto,
  })
  async getMySubscriptions(@UserInfo() user: User): Promise<SubscriptionsResponseDto> {
    return await this.subscriptionService.getUserSubscriptions(user.id);
  }

  @Delete(':subscriptionId')
  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Subscription cancelled successfully',
  })
  async cancelSubscription(
    @Param('subscriptionId') subscriptionId: string, 
    @UserInfo() user: User,
  ) {
    await this.subscriptionService.cancelSubscription(user.id, subscriptionId);
    return {
      message: 'Subscription cancelled successfully',
    };
  }
}
