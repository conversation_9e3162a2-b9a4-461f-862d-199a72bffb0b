import { Injectable } from '@nestjs/common';
import { PlansEntityModel } from '../common/db/plans.model';
import { FeatureEntity, FeatureTypeEnum, PlansEntity } from '../common/entities/plans.entity';

@Injectable()
export class UserPlanRepository {
  async getPlans(): Promise<PlansEntity[]> {
    const searchQuery = { isDeleted: false };
    const planDoc = await PlansEntityModel.find(searchQuery).exec();
    return planDoc.map((item) => item.toObject());
  }

  async getPlanById(planId: string): Promise<PlansEntity | null> {
    const searchQuery = { id: planId, isDeleted: false };
    const planDoc = await PlansEntityModel.findOne(searchQuery).exec();
    return planDoc !== null ? planDoc.toObject() : null;
  }

  async getFeatureByFeatureType(featureType: FeatureTypeEnum): Promise<FeatureEntity | null> {
    // Query the nested features array and return only the matched element
    const doc = await PlansEntityModel.findOne(
      { 'features.featureType': featureType, isDeleted: false, isActive: true },
      { features: { $elemMatch: { featureType } } }
    ).lean();

    const matched = (doc as any)?.features?.[0];
    return matched ?? null;
  }

  async findAllActivePlans(): Promise<PlansEntity[]> {
    const searchQuery = { isDeleted: false, isActive: true };
    const planDoc = await PlansEntityModel.find(searchQuery).exec();
    return planDoc.map((item) => item.toObject());
  }

}
